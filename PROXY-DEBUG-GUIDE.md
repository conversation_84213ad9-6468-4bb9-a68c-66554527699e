# 🔧 代理模式调试指南

## 📋 当前问题排查

您启用了代理模式但仍然遇到CORS问题，这说明代理配置可能没有正确生效。

## 🛠 立即排查步骤

### 1. **重启开发服务器**
```bash
# 停止当前服务器 (Ctrl+C)
# 然后重新启动
pnpm dev
```

### 2. **检查控制台日志**
打开浏览器开发者工具，查看以下日志：
- `使用代理模式构建URL:` - 应该看到这个日志
- `构建代理URL，原始baseURL:` - 应该显示您的API地址
- `构建的代理URL:` - 应该显示类似 `/api/gemini/xxx` 的代理地址

### 3. **验证代理配置**
在浏览器中访问：`http://localhost:3000/test-proxy.html`
- 点击"测试Gemini代理"按钮
- 如果代理工作正常，应该看到不同的错误（如认证错误而非CORS错误）

### 4. **检查网络请求**
在开发者工具的Network标签中：
- 查看请求URL是否为 `localhost:3000/api/gemini/xxx`
- 如果仍然显示 `googleapis.com` 则代理没有生效

## 🔍 详细调试步骤

### 步骤1：确认配置保存
在API配置页面：
1. 启用代理模式开关
2. 点击"保存配置"按钮（重要！）
3. 在控制台查看是否有 `测试连接，当前配置:` 日志

### 步骤2：检查配置是否生效
在浏览器控制台执行：
```javascript
// 检查当前API配置
console.log('当前配置:', localStorage.getItem('apiConfig'))
```

应该看到包含 `"enableProxy":true` 的配置。

### 步骤3：手动测试代理
在控制台执行：
```javascript
// 测试代理端点
fetch('/api/gemini/v1beta/models', {
  headers: { 'Authorization': 'Bearer YOUR_API_KEY' }
}).then(r => console.log('代理响应:', r.status))
  .catch(e => console.log('代理错误:', e))
```

### 步骤4：检查Vite代理配置
确认 `vite.config.js` 中的代理配置存在：
```javascript
proxy: {
  '/api/gemini': {
    target: 'https://generativelanguage.googleapis.com',
    changeOrigin: true,
    rewrite: (path) => path.replace(/^\/api\/gemini/, '')
  }
}
```

## 🚨 常见问题解决

### 问题1：代理开关不生效
**解决方案：**
1. 确保点击了"保存配置"按钮
2. 刷新页面重新加载配置
3. 检查控制台是否有错误信息

### 问题2：Vite代理未启动
**解决方案：**
1. 完全停止开发服务器（Ctrl+C）
2. 重新运行 `pnpm dev`
3. 确认端口为3000

### 问题3：仍然看到CORS错误
**解决方案：**
1. 检查请求URL是否以 `/api/gemini` 开头
2. 如果不是，说明代理逻辑有问题
3. 查看控制台的构建URL日志

## 📞 如果仍然失败

如果按照以上步骤仍然无法解决：

1. **提供详细信息：**
   - 浏览器控制台的完整错误信息
   - Network标签中的请求URL
   - `localStorage.getItem('apiConfig')` 的输出

2. **尝试备用方案：**
   - 使用91hub等中转服务：`https://ai.91hub.vip/v1`
   - 暂时关闭代理模式，使用支持CORS的API服务

3. **联系技术支持：**
   - 提供完整的错误截图
   - 说明操作步骤和环境信息

## 🎯 成功标志

代理正常工作时您应该看到：
- ✅ 控制台显示"使用代理模式构建URL"
- ✅ Network请求显示 `localhost:3000/api/gemini/xxx`
- ✅ 错误信息变为认证错误而非CORS错误
- ✅ 测试连接显示"连接测试成功（代理模式）"

现在请按照这个指南逐步排查问题！