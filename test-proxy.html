<!DOCTYPE html>
<html>
<head>
    <title>代理测试</title>
</head>
<body>
    <h1>API代理测试</h1>
    <button onclick="testProxy()">测试Gemini代理</button>
    <button onclick="testDirect()">测试直连</button>
    <div id="result"></div>

    <script>
        async function testProxy() {
            const result = document.getElementById('result');
            result.innerHTML = '测试代理中...';
            
            try {
                // 测试代理端点
                const response = await fetch('/api/gemini/v1beta/models', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer YOUR_API_KEY'
                    }
                });
                
                if (response.ok) {
                    const data = await response.text();
                    result.innerHTML = `<h3>代理测试成功!</h3><pre>${data}</pre>`;
                } else {
                    result.innerHTML = `<h3>代理测试失败:</h3><p>${response.status} - ${response.statusText}</p>`;
                }
            } catch (error) {
                result.innerHTML = `<h3>代理测试错误:</h3><p>${error.message}</p>`;
            }
        }

        async function testDirect() {
            const result = document.getElementById('result');
            result.innerHTML = '测试直连中...';
            
            try {
                // 测试直连端点
                const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer YOUR_API_KEY'
                    }
                });
                
                if (response.ok) {
                    const data = await response.text();
                    result.innerHTML = `<h3>直连测试成功!</h3><pre>${data}</pre>`;
                } else {
                    result.innerHTML = `<h3>直连测试失败:</h3><p>${response.status} - ${response.statusText}</p>`;
                }
            } catch (error) {
                result.innerHTML = `<h3>直连测试错误:</h3><p>${error.message}</p>`;
                console.error('直连错误详情:', error);
            }
        }
    </script>
</body>
</html>