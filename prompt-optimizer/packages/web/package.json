{"name": "@prompt-optimizer/web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:unit": "vitest run tests/unit"}, "dependencies": {"@prompt-optimizer/ui": "workspace:*", "element-plus": "^2.9.3", "uuid": "^11.0.5", "vue": "^3.5.13"}, "devDependencies": {"@pinia/testing": "^0.1.7", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@vitejs/plugin-basic-ssl": "^1.2.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.20", "jsdom": "^26.0.0", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "vite": "^6.0.7", "vitest": "^3.0.2", "dotenv": "^16.4.7", "js-yaml": "^4.1.0"}}