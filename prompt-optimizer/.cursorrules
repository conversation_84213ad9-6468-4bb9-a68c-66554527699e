# AI编程规则指南

## 1. 开发环境规范

### 1.1 系统环境
- 当前为Windows系统环境
- 使用Windows命令行语法
- 注意路径分隔符使用 `\` 而非 `/`

### 1.2 测试规范
- 每次代码修改后必须执行 `npm run test`
- 确保所有测试用例通过
- 新功能必须包含对应的测试用例
- 测试覆盖：
  - 单元测试
  - 集成测试
  - 异常场景测试

## 2. 文档管理规范

### 2.1 经验文档管理
- 位置：`experience.md`
- 记录内容：
  - 复用组件信息
  - 依赖库版本
  - 模型配置信息
  - Bug修复经验
  - 最佳实践总结
- 分类存储：
  - 架构设计
  - 错误处理
  - 测试规范
  - Vue开发
  - 工具配置
  - 重构经验

### 2.2 草稿本使用规范
位置：`scratchpad.md`

#### 任务记录格式
```markdown
## 任务：[任务名称] - [日期]
### 目标
[任务目标描述]

### 计划步骤
[ ] 1. [具体步骤]
    - 预期结果：
    - 风险评估：
[x] 2. [已完成步骤]
    - 完成时间：
    - 实际结果：

### 问题记录
1. [问题描述]
   - 原因：
   - 解决方案：
   - 经验总结：

### 里程碑
- [x] [已完成里程碑]
- [ ] [待完成里程碑]
```

## 3. 代码规范

### 3.1 API集成规范
- 业务逻辑与API配置解耦
- 统一使用OpenAI兼容格式
- 独立管理提示词模板
- 敏感信息使用环境变量

### 3.2 错误处理规范
```typescript
try {
  await apiCall();
} catch (err) {
  console.error("[错误类型]", err.context);
  throw new Error("友好的错误提示");
}
```

### 3.3 类型定义规范
```typescript
interface ModelConfig {
  name: string;      // 必填
  baseURL: string;   // 必填
  models: string[];  // 必填
}
```

## 4. 工作流程规范

### 4.1 新功能开发流程
1. 需求文档分析
2. 技术方案设计
3. 编写测试用例
4. 功能实现
5. 测试验证
6. 文档更新

### 4.2 Bug修复流程
1. 问题复现与分析
2. 制定修复方案
3. 编写测试用例
4. 实施修复
5. 验证修复效果
6. 更新经验文档

### 4.3 代码审查要点
1. 类型安全
2. 错误处理
3. 测试覆盖
4. 代码风格
5. 性能影响

## 5. 项目文档结构
必读文档：
- `fileNames.md`：项目地图
- `docs/prd.md`：产品需求
- `docs/app-flow.md`：应用流程
- `docs/tech-stack.md`：技术栈
- `docs/file-structure.md`：文件结构
- `docs/frontend-guidelines.md`：前端指南

## 6. 会话管理规范

### 6.1 开始阶段
1. 检查任务上下文
2. 确认开发环境
3. 制定实施计划

### 6.2 执行阶段
1. 步骤确认
2. 代码生成
3. 测试验证
4. 文档更新

### 6.3 结束阶段
1. 总结完成内容
2. 记录遇到的问题
3. 更新经验文档
4. 规划下次任务

## 7. 上下文管理
1. 聚焦关键信息
2. 避免无关操作
3. 保持响应精确
4. 复用已有方案
5. 及时同步文档