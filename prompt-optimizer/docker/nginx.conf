server {
    listen 80;
    server_name _;
    root /usr/share/nginx/html;
    index index.html;

    # 安全相关头部
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' https: http: data: blob: 'unsafe-inline'" always;

    # 启用gzip压缩
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml application/json application/javascript application/xml+rss text/javascript application/x-javascript image/svg+xml;
    gzip_min_length 1000;

    # 客户端缓存控制
    location /assets {
        expires 7d;
        add_header Cache-Control "public, no-transform";
        try_files $uri $uri/ =404;
    }

    # API请求代理示例（如果需要的话取消注释）
    # location /api {
    #     proxy_pass http://api_backend;
    #     proxy_http_version 1.1;
    #     proxy_set_header Upgrade $http_upgrade;
    #     proxy_set_header Connection 'upgrade';
    #     proxy_set_header Host $host;
    #     proxy_cache_bypass $http_upgrade;
    # }

    # SPA应用路由支持
    location / {
        # 引入Basic认证配置
        include /etc/nginx/conf.d/auth.conf;
        
        try_files $uri $uri/ /index.html;
        expires -1;
        add_header Cache-Control "no-store, no-cache, must-revalidate";
    }

    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 错误页面配置
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }

    # 性能优化：关闭访问日志，只记录错误
    access_log off;
    error_log /var/log/nginx/error.log error;

    # 禁止特定请求方法
    if ($request_method !~ ^(GET|HEAD|POST|PUT|DELETE|OPTIONS)$) {
        return 444;
    }

    # 配置较大文件的传输
    client_max_body_size 50m;
    client_body_buffer_size 128k;
    
    # 连接超时设置
    keepalive_timeout 65;
    client_header_timeout 60;
    client_body_timeout 60;
    send_timeout 60;
    proxy_connect_timeout 60;
    proxy_send_timeout 60;
    proxy_read_timeout 60;
} 