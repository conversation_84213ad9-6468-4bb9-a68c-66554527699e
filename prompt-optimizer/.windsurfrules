# 环境说明
当前是windows系统，注意使用windows命令行而非linux命令行。

# 使用草稿本
请读取scratchpad.md文件。
你应该使用 `scratchpad.md` 文件作为草稿本来组织你的想法。特别是当你收到新任务时，你应该首先查看草稿本的内容，必要时清除旧的不同任务，先解释任务内容，然后规划完成任务所需的步骤。你可以使用待办标记来指示进度，例如：
[X] 任务1
[ ] 任务2
当你完成一个子任务时，也要在草稿本中更新任务进度。
特别是当你完成一个里程碑时，使用草稿本来反思和规划将有助于提高你的任务完成深度。
目标是帮助你同时掌握任务的全局视图和进度。在规划下一步时始终参考草稿本。

# 使用经验
请读取experience.md文件。
在与用户交互过程中，如果你发现项目中有任何可重用的内容（例如：库的版本、模型名称），特别是关于你犯的错误的修复或收到的纠正，你应该在 `experience.md` 文件做记录，以避免再次犯同样的错误。




