{"buildCommand": "if [[ $(pwd) == */packages/extension ]]; then cd ../.. && pnpm build && mkdir -p packages/extension/packages/web && cp -r packages/web/dist packages/extension/packages/web/dist; else pnpm build; fi", "outputDirectory": "packages/web/dist", "installCommand": "pwd && if [[ $(pwd) == */packages/extension ]]; then cd ../.. && pnpm install && pnpm i @vercel/analytics -w; else pnpm install && pnpm i @vercel/analytics -w; fi", "rewrites": [{"source": "/api/:path*", "destination": "/api/:path*"}, {"source": "/(.*)", "destination": "/index.html"}], "github": {"silent": true}, "env": {"VITE_VERCEL_DEPLOYMENT": "true"}, "build": {"env": {"VITE_VERCEL_DEPLOYMENT": "true"}}}