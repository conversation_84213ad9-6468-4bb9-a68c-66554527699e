{"name": "prompt-optimizer", "version": "1.1.0", "private": true, "packageManager": "pnpm@10.6.1", "engines": {"node": "^18.0.0 || ^20.0.0 || ^22.0.0", "npm": "请使用pnpm代替npm", "yarn": "请使用pnpm代替yarn"}, "scripts": {"build:core": "pnpm -F @prompt-optimizer/core build", "build:ui": "pnpm -F @prompt-optimizer/ui build", "build:web": "pnpm -F @prompt-optimizer/web build", "build:ext": "pnpm -F @prompt-optimizer/extension build", "build": "pnpm -r build", "watch:ui": "pnpm -F @prompt-optimizer/ui build --watch", "dev": "npm-run-all dev:setup dev:parallel", "dev:setup": "npm-run-all clean:dist build:core build:ui", "dev:parallel": "concurrently -k -p \"[{name}]\" -n \"UI,WEB\" \"pnpm run watch:ui\" \"pnpm run dev:web\"", "dev:web": "pnpm -F @prompt-optimizer/web dev", "dev:ext": "pnpm -F @prompt-optimizer/extension dev", "test:core": "pnpm -F @prompt-optimizer/core test --run --passWithNoTests", "test:ui": "pnpm -F @prompt-optimizer/ui test --run --passWithNoTests", "test:web": "pnpm -F @prompt-optimizer/web test --run --passWithNoTests", "test:ext": "pnpm -F @prompt-optimizer/extension test --run --passWithNoTests", "test": "pnpm -r test --run --passWithNoTests", "test:watch": "pnpm -r test --watch --passWithNoTests", "clean:core": "rimraf packages/core/dist", "clean:ui": "rimraf packages/ui/dist", "clean:web": "rimraf packages/web/dist", "clean:ext": "rimraf packages/extension/dist", "clean:vite:core": "rimraf packages/core/node_modules/.vite", "clean:vite:ui": "rimraf packages/ui/node_modules/.vite", "clean:vite:web": "rimraf packages/web/node_modules/.vite", "clean:vite:ext": "rimraf packages/extension/node_modules/.vite", "clean:vite": "npm-run-all clean:vite:core clean:vite:ui clean:vite:web clean:vite:ext", "clean:dist": "npm-run-all clean:core clean:ui clean:web clean:ext", "clean": "npm-run-all clean:dist clean:vite", "setup:install": "pnpm install", "dev:fresh": "npm-run-all clean setup:install dev", "version:sync": "node scripts/sync-versions.js", "version": "pnpm run version:sync && git add -A"}, "devDependencies": {"@intlify/unplugin-vue-i18n": "^6.0.3", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "i18next": "^24.2.2", "i18next-browser-languagedetector": "^8.0.4", "lodash-unified": "^1.0.3", "npm-run-all": "^4.1.5", "rimraf": "^4.4.1", "typescript": "^5.8.2"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@floating-ui/core": "^1.6.9", "@floating-ui/dom": "^1.6.13", "@floating-ui/utils": "^0.2.9", "@popperjs/core": "^2.11.8", "@vue/reactivity": "^3.5.13", "@vue/runtime-core": "^3.5.13", "@vue/runtime-dom": "^3.5.13", "@vue/shared": "^3.5.13", "@vueuse/core": "^12.7.0", "@vueuse/shared": "^12.7.0", "async-validator": "^4.2.5", "dayjs": "^1.11.13", "lodash-es": "^4.17.21", "memoize-one": "^6.0.0", "normalize-wheel-es": "^1.2.0", "vue-i18n": "^10.0.6"}}