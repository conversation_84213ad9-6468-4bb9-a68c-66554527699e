# API连接问题诊断和修复指南

## 🔍 问题诊断

### 常见错误类型：

#### 1. CORS跨域错误
**错误信息：** `Access to fetch at '...' from origin 'http://localhost:3000' has been blocked by CORS policy`

**原因：** 浏览器直接调用某些API服务（如Google APIs）会遇到跨域限制

**解决方案：**
- ✅ 使用支持跨域的中转服务（推荐：91hub）
- ✅ 使用专门的API代理服务
- ❌ 不要直接使用 `googleapis.com` 地址

#### 2. API认证错误 
**错误信息：** `401 Unauthorized` 或 `API密钥无效`

**原因：** API密钥失效、错误或权限不足

**解决方案：**
- 检查API密钥是否正确输入
- 确认API密钥仍然有效
- 检查账户余额和配额
- 验证密钥权限设置

#### 3. 网络连接错误
**错误信息：** `net::ERR_FAILED` 或 `NetworkError`

**原因：** 网络连接问题或API服务不可用

**解决方案：**
- 检查网络连接
- 确认API服务地址正确
- 测试其他网络环境

## 🛠 推荐配置

### 最佳实践配置：

```javascript
// 推荐配置
{
  "apiKey": "你的有效API密钥",
  "baseURL": "https://ai.91hub.vip/v1",
  "selectedModel": "gemini-2.5-flash",
  "maxTokens": 2000000,
  "temperature": 0.7
}
```

### 支持的API服务：

1. **91hub中转服务** (推荐)
   - 地址：`https://ai.91hub.vip/v1`
   - 支持多种模型
   - 解决CORS问题
   - 购买地址：[淘宝链接](https://item.taobao.com/item.htm?ft=t&id=938261705242)

2. **OpenAI官方**
   - 地址：`https://api.openai.com/v1`
   - 需要科学上网
   - 可能遇到地区限制

3. **其他中转服务**
   - 确保支持OpenAI格式
   - 检查是否解决CORS问题

### 推荐模型选择：

1. **高性价比：** `gemini-2.5-flash`
2. **高质量：** `gemini-2.5-pro`, `claude-4-sonnet`
3. **中文优化：** `deepseek-chat`, `deepseek-reasoner`
4. **经典稳定：** `gpt-3.5-turbo`, `gpt-4o-mini`

## 🔧 故障排除步骤

### 步骤1：检查基本配置
- [ ] API密钥已正确输入
- [ ] API地址格式正确
- [ ] 模型名称匹配

### 步骤2：测试连接
- [ ] 点击"测试连接"按钮
- [ ] 查看控制台错误信息
- [ ] 确认网络连通性

### 步骤3：更换配置
如果测试失败，尝试：
- [ ] 更换为91hub中转服务
- [ ] 使用推荐的模型
- [ ] 重置配置到默认值

### 步骤4：联系支持
如果仍然失败：
- [ ] 联系API服务提供方
- [ ] 查看服务状态页面
- [ ] 检查账户和配额状态

## 📞 技术支持

遇到问题可以：
1. 查看浏览器控制台详细错误信息
2. 尝试更换网络环境
3. 联系API服务商技术支持
4. 参考API服务商的官方文档

## 🔄 更新日志

- 修复了CORS跨域问题的检测和提示
- 添加了推荐的API配置
- 优化了错误信息显示
- 提供了详细的故障排除指南