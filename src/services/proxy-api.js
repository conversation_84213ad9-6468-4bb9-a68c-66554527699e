// 代理API服务 - 解决跨域问题的专用服务
import { ElMessage } from 'element-plus'

class ProxyAPIService {
  constructor() {
    this.proxyEndpoints = {
      development: {
        gemini: '/api/gemini',
        openai: '/api/openai',
        hub91: '/api/91hub',
        generic: '/api/proxy'
      },
      production: {
        // 生产环境使用Vercel函数或Netlify函数
        gemini: '/api/gemini-proxy',
        openai: '/api/openai-proxy', 
        hub91: '/api/91hub-proxy',
        generic: '/api/generic-proxy'
      }
    }
  }

  // 检测API类型
  detectAPIType(baseURL) {
    if (baseURL.includes('googleapis.com')) return 'gemini'
    if (baseURL.includes('api.openai.com')) return 'openai'
    if (baseURL.includes('91hub.vip')) return 'hub91'
    return 'generic'
  }

  // 获取代理端点
  getProxyEndpoint(baseURL, endpoint) {
    const env = import.meta.env.DEV ? 'development' : 'production'
    const apiType = this.detectAPIType(baseURL)
    return this.proxyEndpoints[env][apiType] + endpoint
  }

  // 代理请求
  async makeProxyRequest(originalURL, endpoint, options = {}) {
    try {
      const proxyURL = this.getProxyEndpoint(originalURL, endpoint)
      
      const requestOptions = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        ...options
      }

      // 对于通用代理，添加目标URL头
      if (this.detectAPIType(originalURL) === 'generic') {
        requestOptions.headers['X-Target-URL'] = originalURL + endpoint
      }

      console.log('代理请求:', proxyURL, requestOptions)

      const response = await fetch(proxyURL, requestOptions)
      
      if (!response.ok) {
        throw new Error(`代理请求失败: ${response.status} - ${response.statusText}`)
      }

      return response
    } catch (error) {
      console.error('代理请求错误:', error)
      throw error
    }
  }

  // 流式代理请求
  async makeStreamProxyRequest(originalURL, endpoint, options = {}, onChunk = null) {
    try {
      const response = await this.makeProxyRequest(originalURL, endpoint, options)
      
      if (!response.body) {
        throw new Error('响应体不支持流式读取')
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let fullContent = ''

      while (true) {
        const { done, value } = await reader.read()
        
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        fullContent += chunk
        
        if (onChunk) {
          onChunk(chunk, fullContent)
        }
      }

      return fullContent
    } catch (error) {
      console.error('流式代理请求错误:', error)
      throw error
    }
  }

  // 测试代理连接
  async testProxyConnection(baseURL, apiKey) {
    try {
      const testEndpoint = baseURL.includes('googleapis.com') ? '/v1beta/models' : '/models'
      
      const response = await this.makeProxyRequest(baseURL, testEndpoint, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${apiKey}`
        }
      })

      const data = await response.json()
      console.log('代理连接测试成功:', data)
      return true
    } catch (error) {
      console.error('代理连接测试失败:', error)
      return false
    }
  }
}

// CORS检测工具
export class CORSDetector {
  static async checkCORS(url) {
    try {
      const response = await fetch(url, {
        method: 'HEAD',
        mode: 'cors'
      })
      return { 
        hasCORS: true, 
        status: response.status 
      }
    } catch (error) {
      if (error.message.includes('CORS')) {
        return { 
          hasCORS: false, 
          error: 'CORS blocked',
          needsProxy: true 
        }
      }
      return { 
        hasCORS: false, 
        error: error.message 
      }
    }
  }

  static async suggestSolution(baseURL) {
    const corsCheck = await this.checkCORS(baseURL)
    
    if (corsCheck.needsProxy) {
      ElMessage.warning({
        message: 'CORS跨域问题，已自动切换到代理模式',
        duration: 3000
      })
      return 'proxy'
    } else if (corsCheck.hasCORS) {
      ElMessage.success('直连API可用')
      return 'direct'
    } else {
      ElMessage.error('API连接失败：' + corsCheck.error)
      return 'error'
    }
  }
}

export default new ProxyAPIService()