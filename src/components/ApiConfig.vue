<template>
  <div class="api-config">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>API配置</span>
          <el-tag :type="isApiConfigured ? 'success' : 'danger'" size="small">
            {{ isApiConfigured ? '已配置' : '未配置' }}
          </el-tag>
        </div>
      </template>
      
      <el-form :model="configForm" label-width="100px" size="small">
        <el-form-item label="API密钥">
          <el-input
            v-model="configForm.apiKey"
            type="password"
            placeholder="请输入OpenAI API密钥"
            show-password
            clearable
          />
        </el-form-item>
        
        <el-form-item label="API地址">
          <el-input
            v-model="configForm.baseURL"
            placeholder="https://api.openai.com/v1"
            clearable
          />
          <div class="api-url-tips">
            <el-alert 
              title="⚠️ 重要提示" 
              type="warning" 
              :closable="false"
              style="margin-top: 8px;">
              <div>
                <p><strong>如果使用Google Gemini API：</strong></p>
                <ul style="margin: 5px 0; padding-left: 20px;">
                  <li>浏览器直接调用会遇到CORS跨域问题</li>
                  <li>建议使用中转服务或代理API</li>
                  <li>推荐使用91hub等支持OpenAI格式的服务</li>
                </ul>
                <p><strong>推荐的API地址格式：</strong></p>
                <ul style="margin: 5px 0; padding-left: 20px;">
                  <li>OpenAI官方：https://api.openai.com/v1</li>
                  <li>91hub中转：https://ai.91hub.vip/v1</li>
                  <li>其他中转服务：请确保支持OpenAI格式</li>
                </ul>
              </div>
            </el-alert>
          </div>
        </el-form-item>
        
        <el-form-item label="模型选择">
          <el-select v-model="configForm.selectedModel" placeholder="选择模型">
            <el-option
              v-for="model in availableModels"
              :key="model.id"
              :label="model.name"
              :value="model.id"
            >
              <span>{{ model.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 12px">
                {{ model.description }}
              </span>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="自定义模型">
          <div style="display: flex; gap: 8px; align-items: center;">
            <el-input
              v-model="customModelInput"
              placeholder="输入自定义模型名称，如：gpt-4o-mini"
              style="flex: 1;"
            />
            <el-button 
              type="primary" 
              size="small" 
              @click="addCustomModel"
              :disabled="!customModelInput.trim()"
            >
              添加
            </el-button>
          </div>
          <div v-if="customModels.length > 0" style="margin-top: 8px;">
            <el-tag
              v-for="model in customModels"
              :key="model.id"
              closable
              @close="removeCustomModel(model.id)"
              style="margin-right: 8px; margin-bottom: 4px;"
            >
              {{ model.name }}
            </el-tag>
          </div>
        </el-form-item>
        
        <el-form-item label="最大Token">
          <div class="max-tokens-control">
            <el-checkbox 
              v-model="configForm.unlimitedTokens" 
              @change="handleUnlimitedTokensChange"
              style="margin-bottom: 8px;"
            >
              无限制
            </el-checkbox>
            <el-input-number
              v-model="configForm.maxTokens"
              :min="100"
              :max="10000000"
              :step="1000"
              :disabled="configForm.unlimitedTokens"
              controls-position="right"
              placeholder="无限制"
            />
          </div>
        </el-form-item>
        
        <el-form-item label="创造性">
          <el-slider
            v-model="configForm.temperature"
            :min="0"
            :max="1"
            :step="0.1"
            show-tooltip
            :format-tooltip="formatTemperature"
          />
        </el-form-item>

        <el-form-item label="代理模式">
          <div style="display: flex; flex-direction: column; gap: 8px;">
            <el-switch
              v-model="configForm.enableProxy"
              active-text="启用代理（解决CORS问题）"
              inactive-text="直连模式"
              @change="handleProxyModeChange"
            />
            <el-alert 
              v-if="configForm.enableProxy"
              title="代理模式说明" 
              type="info" 
              :closable="false"
              style="margin-top: 4px;">
              <div style="font-size: 12px;">
                <p>✅ 代理模式可解决浏览器CORS跨域问题</p>
                <p>✅ 特别适用于Google Gemini等API服务</p>
                <p>⚠️ 仅在开发环境下自动启用Vite代理</p>
              </div>
            </el-alert>
          </div>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="saveConfig" :loading="validating">
            {{ validating ? '验证中...' : '保存配置' }}
          </el-button>
          <el-button @click="testConnection" :loading="validating">
            测试连接
          </el-button>
          <el-button @click="resetConfig">重置</el-button>
        </el-form-item>
      </el-form>
      
      <el-divider />
      
      <div class="config-tips">
        <h4>配置说明：</h4>
        <ul>
          <li><strong>模型选择：</strong>推荐使用gemini2.5pro、claude3.7/4</li>
          <li><strong>国产模型：</strong>国产模型推荐使用阿里百炼API速度比较快</li>
          <li><strong>最佳模型：</strong><span style="color: red;">gemini、claude中转购买地址：<a href="https://item.taobao.com/item.htm?ft=t&id=938261705242" target="_blank">https://item.taobao.com/item.htm?ft=t&id=938261705242</a></span></li>
          <li><strong>最大Token：</strong>控制生成内容的长度</li>
          <li><strong>创造性：</strong>0表示更准确，1表示更有创意</li>
        </ul>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useNovelStore } from '../stores/novel.js'
import apiService from '../services/api.js'

const store = useNovelStore()
const validating = ref(false)
const customModelInput = ref('')
const customModels = ref([])

const configForm = reactive({
  apiKey: '',
  baseURL: 'https://ai.91hub.vip/v1',  // 修改为推荐的中转服务
  selectedModel: 'gemini-2.5-flash',  // 推荐的高性价比模型
  maxTokens: 2000000, // 默认最大Token数
  unlimitedTokens: false, // 默认不无限制
  temperature: 0.7,
  enableProxy: false // 代理模式开关
})

const defaultModels = [
  {
    id: 'gemini-2.5-flash',
    name: 'Gemini 2.5 Flash',
    description: '推荐 - 快速响应，高性价比'
  },
  {
    id: 'gemini-2.5-pro',
    name: 'Gemini 2.5 Pro',
    description: '推荐 - 高质量输出'
  },
  {
    id: 'deepseek-reasoner',
    name: 'DeepSeek R1',
    description: '推理能力强，适合复杂任务'
  },
  {
    id: 'deepseek-chat',
    name: 'DeepSeek V3',
    description: '对话生成，中文友好'
  },
  {
    id: 'claude-3.7-sonnet',
    name: 'Claude 3.7 Sonnet',
    description: '文学创作优秀'
  },
  {
    id: 'claude-4-sonnet',
    name: 'Claude 4 Sonnet',
    description: '最新版本，能力全面'
  },
  {
    id: 'gpt-3.5-turbo',
    name: 'GPT-3.5 Turbo',
    description: '经典模型，稳定可靠'
  },
  {
    id: 'gpt-4o-mini',
    name: 'GPT-4o Mini',
    description: '轻量版GPT-4，性价比高'
  }
]

const availableModels = computed(() => {
  return [...defaultModels, ...customModels.value]
})

const isApiConfigured = computed(() => store.isApiConfigured)

const formatTemperature = (value) => {
  if (value <= 0.3) return '保守'
  if (value <= 0.7) return '平衡'
  return '创新'
}

// 处理无限制Token选项
const handleUnlimitedTokensChange = () => {
  if (configForm.unlimitedTokens) {
    configForm.maxTokens = null
  } else {
    configForm.maxTokens = 2000000 // 恢复到用户设定的默认值
  }
}

const addCustomModel = () => {
  const modelName = customModelInput.value.trim()
  if (!modelName) return
  
  // 检查是否已存在
  const exists = availableModels.value.some(model => model.id === modelName)
  if (exists) {
    ElMessage.warning('该模型已存在')
    return
  }
  
  // 添加自定义模型
  customModels.value.push({
    id: modelName,
    name: modelName,
    description: '自定义模型'
  })
  
  customModelInput.value = ''
  ElMessage.success('自定义模型添加成功')
  
  // 保存到本地存储
  saveCustomModels()
}

const removeCustomModel = (modelId) => {
  const index = customModels.value.findIndex(model => model.id === modelId)
  if (index > -1) {
    customModels.value.splice(index, 1)
    
    // 如果当前选中的模型被删除，重置为默认模型
    if (configForm.selectedModel === modelId) {
      configForm.selectedModel = 'gpt-3.5-turbo'
    }
    
    ElMessage.success('自定义模型删除成功')
    saveCustomModels()
  }
}

const saveCustomModels = () => {
  localStorage.setItem('customModels', JSON.stringify(customModels.value))
}

const loadCustomModels = () => {
  const saved = localStorage.getItem('customModels')
  if (saved) {
    try {
      customModels.value = JSON.parse(saved)
    } catch (error) {
      console.error('加载自定义模型失败:', error)
    }
  }
}

const saveConfig = async () => {
  if (!configForm.apiKey) {
    ElMessage.warning('请输入API密钥')
    return
  }
  
  validating.value = true
  try {
    // 更新配置
    store.updateApiConfig(configForm)
    
    // 验证API密钥
    const isValid = await store.validateApiKey()
    
    if (isValid) {
      ElMessage.success('API配置保存成功')
      // 保存到本地存储
      localStorage.setItem('apiConfig', JSON.stringify(configForm))
    } else {
      ElMessage.error('API密钥验证失败，请检查配置')
    }
  } catch (error) {
    ElMessage.error('配置保存失败：' + error.message)
  } finally {
    validating.value = false
  }
}

const testConnection = async () => {
  if (!configForm.apiKey) {
    ElMessage.warning('请先输入API密钥')
    return
  }
  
  if (!configForm.baseURL) {
    ElMessage.warning('请先输入API地址')
    return
  }
  
  validating.value = true
  
  try {
    console.log('开始连接测试，当前配置:', configForm)
    
    // 使用新的 testConnection 方法进行测试（已集成代理模式）
    const testConfig = {
      apiKey: configForm.apiKey,
      baseURL: configForm.baseURL,
      selectedModel: configForm.selectedModel,
      enableProxy: configForm.enableProxy
    }
    
    const result = await apiService.testConnection(testConfig)
    
    if (result.success) {
      ElMessage.success({
        message: result.message,
        duration: 5000
      })
      console.log('连接测试成功详情:', result)
    } else {
      // 根据错误类型提供不同的提示
      let messageType = 'error'
      if (result.errorType === 'cors') {
        messageType = 'warning'
        ElMessage({
          type: messageType,
          message: result.message + '\n建议：系统已自动尝试代理模式，如仍失败请检查网络连接',
          duration: 8000,
          showClose: true
        })
      } else if (result.errorType === 'auth') {
        ElMessage({
          type: messageType,
          message: result.message + '\n请检查API密钥是否正确配置',
          duration: 6000,
          showClose: true
        })
      } else if (result.errorType === 'network') {
        ElMessage({
          type: messageType,
          message: result.message + '\n请检查API地址格式和网络连接',
          duration: 6000,
          showClose: true
        })
      } else {
        ElMessage({
          type: messageType,
          message: result.message,
          duration: 5000,
          showClose: true
        })
      }
      console.error('连接测试失败详情:', result)
    }
  } catch (error) {
    console.error('连接测试异常:', error)
    ElMessage.error({
      message: '连接测试过程中发生异常：' + error.message,
      duration: 5000,
      showClose: true
    })
  } finally {
    validating.value = false
  }
}

// 处理代理模式切换
const handleProxyModeChange = (enabled) => {
  if (enabled) {
    ElMessage.info('已启用代理模式，将通过本地代理解决CORS问题')
    // 自动检测是否需要代理的API地址
    if (configForm.baseURL.includes('googleapis.com')) {
      ElMessage.success('检测到Google API，代理模式特别适用')
    }
  } else {
    ElMessage.info('已切换到直连模式')
  }
}

const resetConfig = () => {
  Object.assign(configForm, {
    apiKey: '',
    baseURL: 'https://ai.91hub.vip/v1',  // 使用推荐的中转服务
    selectedModel: 'gemini-2.5-flash',  // 推荐的高性价比模型
    maxTokens: 2000000, // 默认最大Token数
    unlimitedTokens: false, // 默认不无限制
    temperature: 0.7,
    enableProxy: false // 重置代理模式
  })
  localStorage.removeItem('apiConfig')
  ElMessage.success('配置已重置')
}

// 加载保存的配置
const loadSavedConfig = () => {
  const saved = localStorage.getItem('apiConfig')
  if (saved) {
    try {
      const config = JSON.parse(saved)
      // 为现有配置添加新字段的默认值
      if (config.unlimitedTokens === undefined) {
        config.unlimitedTokens = config.maxTokens === null
      }
      if (config.enableProxy === undefined) {
        config.enableProxy = false
      }
      Object.assign(configForm, config)
      store.updateApiConfig(config)
    } catch (error) {
      console.error('加载配置失败:', error)
    }
  }
}

onMounted(() => {
  loadCustomModels()
  loadSavedConfig()
})
</script>

<style scoped>
.api-config {
  padding: 20px;
}

.config-card {
  max-width: 600px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-tips {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-top: 20px;
}

.config-tips h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.config-tips ul {
  margin: 0;
  padding-left: 20px;
}

.config-tips li {
  margin-bottom: 8px;
  color: #606266;
  line-height: 1.5;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-slider__runway) {
  margin: 16px 0;
}

.max-tokens-control {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style>