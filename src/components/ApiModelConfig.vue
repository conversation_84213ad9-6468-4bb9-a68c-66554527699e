<template>
  <div class="api-model-config">
    <div class="model-actions">
      <el-button type="danger" size="small" @click="$emit('delete')">
        <el-icon><Delete /></el-icon>
        删除此模型
      </el-button>
    </div>
    
    <el-form :model="localConfig" label-width="100px" size="small">
      <el-form-item label="API密钥" required>
        <el-input
          v-model="localConfig.apiKey"
          type="password"
          placeholder="请输入API密钥"
          show-password
          clearable
          @input="updateConfig"
        />
      </el-form-item>
      
      <el-form-item label="API地址" required>
        <el-input
          v-model="localConfig.baseURL"
          placeholder="https://api.openai.com/v1"
          clearable
          @input="updateConfig"
        />
        <div class="api-url-tips">
          <el-text size="small" type="info">
            推荐地址：OpenAI官方、91hub中转、其他兼容OpenAI格式的服务
          </el-text>
        </div>
      </el-form-item>
      
      <el-form-item label="模型选择">
        <el-select v-model="localConfig.selectedModel" placeholder="选择模型" @change="updateConfig">
          <el-option
            v-for="model in availableModels"
            :key="model.id"
            :label="model.name"
            :value="model.id"
          >
            <span>{{ model.name }}</span>
            <span style="float: right; color: #8492a6; font-size: 12px">
              {{ model.description }}
            </span>
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="自定义模型">
        <div style="display: flex; gap: 8px; align-items: center;">
          <el-input
            v-model="customModelInput"
            placeholder="输入自定义模型名称"
            style="flex: 1;"
          />
          <el-button 
            type="primary" 
            size="small" 
            @click="addCustomModel"
            :disabled="!customModelInput.trim()"
          >
            添加
          </el-button>
        </div>
        <div v-if="localConfig.customModels && localConfig.customModels.length > 0" style="margin-top: 8px;">
          <el-tag
            v-for="model in localConfig.customModels"
            :key="model.id"
            closable
            @close="removeCustomModel(model.id)"
            style="margin-right: 8px; margin-bottom: 4px;"
          >
            {{ model.name }}
          </el-tag>
        </div>
      </el-form-item>
      
      <el-form-item label="最大Token">
        <div class="max-tokens-control">
          <el-checkbox 
            v-model="unlimitedTokens" 
            @change="handleUnlimitedTokensChange"
            style="margin-bottom: 8px;"
          >
            无限制
          </el-checkbox>
          <el-input-number
            v-model="localConfig.maxTokens"
            :min="100"
            :max="10000000"
            :step="1000"
            :disabled="unlimitedTokens"
            controls-position="right"
            placeholder="无限制"
            @change="updateConfig"
          />
        </div>
      </el-form-item>
      
      <el-form-item label="创造性">
        <el-slider
          v-model="localConfig.temperature"
          :min="0"
          :max="1"
          :step="0.1"
          show-tooltip
          :format-tooltip="formatTemperature"
          @change="updateConfig"
        />
      </el-form-item>

      <el-form-item label="代理模式">
        <el-switch
          v-model="localConfig.enableProxy"
          active-text="启用代理（解决CORS问题）"
          inactive-text="直连模式"
          @change="updateConfig"
        />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="testConnection" :loading="testing">
          {{ testing ? '测试中...' : '测试连接' }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import apiService from '../services/api.js'

const props = defineProps({
  modelConfig: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update', 'delete'])

// 响应式数据
const testing = ref(false)
const customModelInput = ref('')

const localConfig = reactive({
  apiKey: '',
  baseURL: 'https://ai.91hub.vip/v1',
  selectedModel: 'gemini-2.5-flash',
  maxTokens: 2000000,
  temperature: 0.7,
  enableProxy: false,
  customModels: [],
  ...props.modelConfig
})

const unlimitedTokens = ref(localConfig.maxTokens === null)

// 默认模型列表
const defaultModels = [
  {
    id: 'gemini-2.5-flash',
    name: 'Gemini 2.5 Flash',
    description: '推荐 - 快速响应，高性价比'
  },
  {
    id: 'gemini-2.5-pro',
    name: 'Gemini 2.5 Pro',
    description: '推荐 - 高质量输出'
  },
  {
    id: 'deepseek-reasoner',
    name: 'DeepSeek R1',
    description: '推理能力强，适合复杂任务'
  },
  {
    id: 'deepseek-chat',
    name: 'DeepSeek V3',
    description: '对话生成，中文友好'
  },
  {
    id: 'claude-3.7-sonnet',
    name: 'Claude 3.7 Sonnet',
    description: '文学创作优秀'
  },
  {
    id: 'claude-4-sonnet',
    name: 'Claude 4 Sonnet',
    description: '最新版本，能力全面'
  },
  {
    id: 'gpt-3.5-turbo',
    name: 'GPT-3.5 Turbo',
    description: '经典模型，稳定可靠'
  },
  {
    id: 'gpt-4o-mini',
    name: 'GPT-4o Mini',
    description: '轻量版GPT-4，性价比高'
  }
]

// 计算属性
const availableModels = computed(() => {
  const customModels = localConfig.customModels || []
  return [...defaultModels, ...customModels]
})

// 监听prop变化
watch(() => props.modelConfig, (newConfig) => {
  Object.assign(localConfig, newConfig)
  unlimitedTokens.value = newConfig.maxTokens === null
}, { deep: true })

// 方法
const formatTemperature = (value) => {
  if (value <= 0.3) return '保守'
  if (value <= 0.7) return '平衡'
  return '创新'
}

const handleUnlimitedTokensChange = () => {
  if (unlimitedTokens.value) {
    localConfig.maxTokens = null
  } else {
    localConfig.maxTokens = 2000000
  }
  updateConfig()
}

const addCustomModel = () => {
  const modelName = customModelInput.value.trim()
  if (!modelName) return
  
  if (!localConfig.customModels) {
    localConfig.customModels = []
  }
  
  // 检查是否已存在
  const exists = availableModels.value.some(model => model.id === modelName)
  if (exists) {
    ElMessage.warning('该模型已存在')
    return
  }
  
  // 添加自定义模型
  localConfig.customModels.push({
    id: modelName,
    name: modelName,
    description: '自定义模型'
  })
  
  customModelInput.value = ''
  ElMessage.success('自定义模型添加成功')
  updateConfig()
}

const removeCustomModel = (modelId) => {
  if (!localConfig.customModels) return
  
  const index = localConfig.customModels.findIndex(model => model.id === modelId)
  if (index > -1) {
    localConfig.customModels.splice(index, 1)
    
    // 如果当前选中的模型被删除，重置为默认模型
    if (localConfig.selectedModel === modelId) {
      localConfig.selectedModel = 'gemini-2.5-flash'
    }
    
    ElMessage.success('自定义模型删除成功')
    updateConfig()
  }
}

const updateConfig = () => {
  emit('update', { ...localConfig })
}

const testConnection = async () => {
  if (!localConfig.apiKey) {
    ElMessage.warning('请先输入API密钥')
    return
  }
  
  if (!localConfig.baseURL) {
    ElMessage.warning('请先输入API地址')
    return
  }
  
  testing.value = true
  try {
    // 临时更新API服务配置进行测试
    const originalConfig = apiService.getConfig()
    apiService.updateConfig(localConfig)
    
    const isValid = await apiService.validateAPIKey()
    
    // 恢复原配置
    apiService.updateConfig(originalConfig)
    
    if (isValid) {
      ElMessage.success('连接测试成功' + (localConfig.enableProxy ? '（代理模式）' : ''))
    } else {
      ElMessage.error('连接测试失败，请检查API密钥和地址配置')
    }
  } catch (error) {
    console.error('连接测试错误:', error)
    if (error.message.includes('CORS')) {
      ElMessage.error('CORS跨域错误：请启用代理模式以解决此问题')
    } else {
      ElMessage.error('连接测试失败：' + error.message)
    }
  } finally {
    testing.value = false
  }
}
</script>

<style scoped>
.api-model-config {
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: #fafafa;
}

.model-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.api-url-tips {
  margin-top: 4px;
}

.max-tokens-control {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-slider__runway) {
  margin: 16px 0;
}
</style>