<template>
  <div class="api-config-groups">
    <!-- 配置组列表 -->
    <div class="config-groups-list">
      <div class="list-header">
        <h3>API配置组管理</h3>
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建配置组
        </el-button>
      </div>
      
      <el-empty v-if="configGroups.length === 0" description="暂无配置组">
        <el-button type="primary" @click="showCreateDialog = true">创建第一个配置组</el-button>
      </el-empty>
      
      <div v-else class="groups-grid">
        <el-card 
          v-for="group in configGroups" 
          :key="group.id"
          :class="['group-card', { 'active': group.isActive, 'disabled': !group.isEnabled }]"
          shadow="hover"
        >
          <template #header>
            <div class="group-header">
              <div class="group-info">
                <h4>{{ group.name }}</h4>
                <el-tag v-if="group.isActive" type="success" size="small">激活中</el-tag>
                <el-tag v-else-if="!group.isEnabled" type="danger" size="small">已禁用</el-tag>
                <el-tag v-else type="info" size="small">待激活</el-tag>
              </div>
              <el-dropdown @command="handleGroupAction">
                <el-button type="text" :icon="MoreFilled" />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="`edit-${group.id}`">
                      <el-icon><Edit /></el-icon>编辑
                    </el-dropdown-item>
                    <el-dropdown-item :command="`duplicate-${group.id}`">
                      <el-icon><CopyDocument /></el-icon>复制
                    </el-dropdown-item>
                    <el-dropdown-item :command="`delete-${group.id}`" divided>
                      <el-icon><Delete /></el-icon>删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
          
          <div class="group-content">
            <p class="group-description">{{ group.description || '暂无描述' }}</p>
            
            <div class="group-stats">
              <div class="stat-item">
                <span class="label">模型数量：</span>
                <span class="value">{{ group.models.length }}</span>
              </div>
              <div class="stat-item">
                <span class="label">创建时间：</span>
                <span class="value">{{ formatDate(group.createdAt) }}</span>
              </div>
            </div>
            
            <div class="group-models">
              <h5>模型配置：</h5>
              <div v-if="group.models.length === 0" class="no-models">
                暂无模型配置
              </div>
              <div v-else class="models-list">
                <el-tag 
                  v-for="(model, index) in group.models.slice(0, 3)" 
                  :key="index"
                  size="small"
                  style="margin-right: 4px; margin-bottom: 4px;"
                >
                  {{ model.selectedModel || '未选择模型' }}
                </el-tag>
                <el-tag v-if="group.models.length > 3" size="small" type="info">
                  +{{ group.models.length - 3 }}
                </el-tag>
              </div>
            </div>
          </div>
          
          <template #footer>
            <div class="group-actions">
              <el-switch
                v-model="group.isEnabled"
                active-text="启用"
                inactive-text="禁用"
                @change="toggleGroupEnabled(group.id)"
              />
              <el-button 
                v-if="!group.isActive && group.isEnabled"
                type="primary" 
                size="small"
                @click="activateGroup(group.id)"
              >
                激活
              </el-button>
              <el-button 
                v-if="group.isActive"
                type="success" 
                size="small"
                disabled
              >
                当前激活
              </el-button>
              <el-button 
                type="text" 
                size="small"
                @click="editGroup(group)"
              >
                配置模型
              </el-button>
            </div>
          </template>
        </el-card>
      </div>
    </div>

    <!-- 创建/编辑配置组对话框 -->
    <el-dialog 
      v-model="showCreateDialog" 
      :title="editingGroup ? '编辑配置组' : '创建配置组'"
      width="600px"
    >
      <el-form :model="groupForm" label-width="100px">
        <el-form-item label="配置组名称" required>
          <el-input v-model="groupForm.name" placeholder="请输入配置组名称" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input 
            v-model="groupForm.description" 
            type="textarea" 
            :rows="2"
            placeholder="请输入配置组描述（可选）" 
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="cancelEdit">取消</el-button>
        <el-button type="primary" @click="saveGroup">
          {{ editingGroup ? '保存' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 模型配置对话框 -->
    <el-dialog 
      v-model="showModelConfigDialog" 
      title="模型配置管理"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="currentEditingGroup">
        <div class="models-header">
          <h4>{{ currentEditingGroup.name }} - 模型配置</h4>
          <el-button type="primary" @click="addNewModel">
            <el-icon><Plus /></el-icon>
            添加模型
          </el-button>
        </div>
        
        <div v-if="currentEditingGroup.models.length === 0" class="no-models-hint">
          <el-empty description="暂无模型配置">
            <el-button type="primary" @click="addNewModel">添加第一个模型</el-button>
          </el-empty>
        </div>
        
        <div v-else class="models-config">
          <el-collapse v-model="activeModelPanels">
            <el-collapse-item 
              v-for="(model, index) in currentEditingGroup.models"
              :key="index"
              :name="index.toString()"
            >
              <template #title>
                <div class="model-title">
                  <span>模型 {{ index + 1 }}: {{ model.selectedModel || '未配置' }}</span>
                  <el-tag v-if="model.apiKey" type="success" size="small">已配置</el-tag>
                  <el-tag v-else type="danger" size="small">未配置</el-tag>
                </div>
              </template>
              
              <ApiModelConfig 
                :model-config="model"
                @update="updateModel(index, $event)"
                @delete="deleteModel(index)"
              />
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showModelConfigDialog = false">完成</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, MoreFilled, Edit, Delete, CopyDocument } from '@element-plus/icons-vue'
import { useNovelStore } from '../stores/novel.js'
import ApiModelConfig from './ApiModelConfig.vue'

const store = useNovelStore()

// 响应式数据
const showCreateDialog = ref(false)
const showModelConfigDialog = ref(false)
const editingGroup = ref(null)
const currentEditingGroup = ref(null)
const activeModelPanels = ref([])

const groupForm = ref({
  name: '',
  description: ''
})

// 计算属性
const configGroups = computed(() => store.apiConfigGroups.configGroups)

// 方法
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const toggleGroupEnabled = (groupId) => {
  store.toggleConfigGroupEnabled(groupId)
  ElMessage.success('配置组状态已更新')
}

const activateGroup = async (groupId) => {
  const success = store.activateConfigGroup(groupId)
  if (success) {
    ElMessage.success('配置组已激活')
  } else {
    ElMessage.error('激活失败，请检查配置组是否已启用且包含有效配置')
  }
}

const editGroup = (group) => {
  currentEditingGroup.value = group
  showModelConfigDialog.value = true
  // 默认展开第一个模型配置
  activeModelPanels.value = group.models.length > 0 ? ['0'] : []
}

const handleGroupAction = (command) => {
  const [action, groupId] = command.split('-')
  const group = configGroups.value.find(g => g.id === groupId)
  
  switch (action) {
    case 'edit':
      editingGroup.value = group
      groupForm.value = {
        name: group.name,
        description: group.description
      }
      showCreateDialog.value = true
      break
      
    case 'duplicate':
      duplicateGroup(group)
      break
      
    case 'delete':
      deleteGroup(groupId)
      break
  }
}

const duplicateGroup = (sourceGroup) => {
  const newGroup = {
    name: `${sourceGroup.name} - 副本`,
    description: sourceGroup.description,
    models: JSON.parse(JSON.stringify(sourceGroup.models)) // 深拷贝模型配置
  }
  
  store.createApiConfigGroup(newGroup)
  ElMessage.success('配置组复制成功')
}

const deleteGroup = async (groupId) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个配置组吗？此操作不可撤销。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    store.deleteApiConfigGroup(groupId)
    ElMessage.success('配置组删除成功')
  } catch {
    // 用户取消删除
  }
}

const saveGroup = () => {
  if (!groupForm.value.name.trim()) {
    ElMessage.warning('请输入配置组名称')
    return
  }
  
  if (editingGroup.value) {
    // 编辑现有配置组
    store.updateApiConfigGroup(editingGroup.value.id, {
      name: groupForm.value.name,
      description: groupForm.value.description
    })
    ElMessage.success('配置组更新成功')
  } else {
    // 创建新配置组
    store.createApiConfigGroup({
      name: groupForm.value.name,
      description: groupForm.value.description,
      models: []
    })
    ElMessage.success('配置组创建成功')
  }
  
  cancelEdit()
}

const cancelEdit = () => {
  showCreateDialog.value = false
  editingGroup.value = null
  groupForm.value = {
    name: '',
    description: ''
  }
}

const addNewModel = () => {
  if (currentEditingGroup.value) {
    const newModel = {
      apiKey: '',
      baseURL: 'https://ai.91hub.vip/v1',
      selectedModel: 'gemini-2.5-flash',
      maxTokens: 2000000,
      temperature: 0.7,
      enableProxy: false,
      customModels: []
    }
    
    currentEditingGroup.value.models.push(newModel)
    const newIndex = currentEditingGroup.value.models.length - 1
    activeModelPanels.value = [newIndex.toString()]
    
    // 更新store
    store.updateApiConfigGroup(currentEditingGroup.value.id, {
      models: currentEditingGroup.value.models
    })
  }
}

const updateModel = (index, updates) => {
  if (currentEditingGroup.value) {
    Object.assign(currentEditingGroup.value.models[index], updates)
    
    // 更新store
    store.updateApiConfigGroup(currentEditingGroup.value.id, {
      models: currentEditingGroup.value.models
    })
  }
}

const deleteModel = async (index) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个模型配置吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    if (currentEditingGroup.value) {
      currentEditingGroup.value.models.splice(index, 1)
      
      // 更新活跃面板
      activeModelPanels.value = activeModelPanels.value.filter(panel => 
        parseInt(panel) < currentEditingGroup.value.models.length
      )
      
      // 更新store
      store.updateApiConfigGroup(currentEditingGroup.value.id, {
        models: currentEditingGroup.value.models
      })
      
      ElMessage.success('模型配置删除成功')
    }
  } catch {
    // 用户取消删除
  }
}
</script>

<style scoped>
.api-config-groups {
  padding: 20px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.list-header h3 {
  margin: 0;
  color: #303133;
}

.groups-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.group-card {
  transition: all 0.3s ease;
}

.group-card:hover {
  transform: translateY(-2px);
}

.group-card.active {
  border-color: #67c23a;
  box-shadow: 0 2px 12px rgba(103, 194, 58, 0.2);
}

.group-card.disabled {
  opacity: 0.6;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.group-info h4 {
  margin: 0 8px 0 0;
  color: #303133;
  font-size: 16px;
}

.group-content {
  padding: 0;
}

.group-description {
  color: #606266;
  font-size: 14px;
  margin: 0 0 15px 0;
  line-height: 1.4;
}

.group-stats {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-bottom: 15px;
}

.stat-item {
  display: flex;
  font-size: 13px;
}

.stat-item .label {
  color: #909399;
  min-width: 80px;
}

.stat-item .value {
  color: #303133;
  font-weight: 500;
}

.group-models h5 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
}

.no-models {
  color: #909399;
  font-size: 13px;
  font-style: italic;
}

.models-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.group-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
}

.models-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.models-header h4 {
  margin: 0;
  color: #303133;
}

.no-models-hint {
  text-align: center;
  padding: 40px 0;
}

.models-config {
  max-height: 500px;
  overflow-y: auto;
}

.model-title {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .groups-grid {
    grid-template-columns: 1fr;
  }
  
  .group-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
}
</style>