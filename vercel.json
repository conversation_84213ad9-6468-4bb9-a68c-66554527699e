{"functions": {"api/proxy.js": {"runtime": "edge"}, "api/stream.js": {"runtime": "edge"}, "api/vercel-status.js": {"runtime": "edge"}}, "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, X-API-KEY"}]}]}