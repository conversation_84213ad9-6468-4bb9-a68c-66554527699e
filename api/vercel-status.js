export const config = {
  runtime: 'edge'
};

export default async function handler(req) {
  // 处理CORS预检请求
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Max-Age': '86400',
      },
    });
  }

  // 只允许GET请求
  if (req.method !== 'GET') {
    return new Response(JSON.stringify({ error: 'Method not allowed' }), {
      status: 405,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }

  try {
    // 检查是否在Vercel环境中
    const isVercelEnvironment = process.env.VERCEL === '1' || 
                               process.env.VERCEL_ENV !== undefined ||
                               req.headers.get('x-vercel-id') !== null;

    // 检查代理功能是否可用
    const proxySupport = true; // 假设代理功能总是可用的

    const response = {
      status: isVercelEnvironment ? 'available' : 'unavailable',
      proxySupport: proxySupport,
      environment: {
        isVercel: isVercelEnvironment,
        runtime: 'edge',
        timestamp: new Date().toISOString()
      }
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
    });
  } catch (error) {
    console.error('检查Vercel状态失败:', error);
    return new Response(JSON.stringify({ 
      error: 'Internal server error',
      status: 'error'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }
}