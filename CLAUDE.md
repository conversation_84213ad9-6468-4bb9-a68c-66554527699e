# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is **91Writing** (AI写作) - A Vue 3-based AI novel writing platform that integrates advanced AI models for professional creative writing. It's a pure frontend application with local data storage that supports multiple AI service providers through OpenAI-compatible APIs.

## Development Commands

### Essential Commands
```bash
# Install dependencies
pnpm install

# Start development server (runs on port 3000)
pnpm dev

# Build for production
pnpm build

# Preview production build
pnpm preview

# Lint and fix code issues
pnpm lint
```

### Development Setup
- Uses **pnpm** as the package manager (preferred over npm)
- Development server runs on `http://localhost:3000`
- Supports hot reload during development
- Production builds are optimized with Vite

## Architecture Overview

### Core Technology Stack
- **Vue 3.3.8** with Composition API
- **Element Plus 2.4.2** for UI components
- **Pinia 2.1.7** for state management
- **Vue Router 4.2.5** for routing
- **Vite 4.5.0** as build tool
- **WangEditor 5.1.23** for rich text editing

### Project Structure
```
src/
├── main.js              # App entry point with Vue app setup
├── App.vue              # Root component
├── router/index.js      # Route definitions with Dashboard layout
├── stores/novel.js      # Main Pinia store for all app state
├── services/
│   ├── api.js          # Core API service for AI integrations
│   └── billing.js      # Token usage tracking
├── components/         # Reusable UI components
├── views/              # Main page components (routed)
├── config/api.json     # API configuration templates
└── style.css          # Global styles
```

### Key Architectural Patterns

1. **Single Store Pattern**: All application state is managed through `useNovelStore()` in `src/stores/novel.js`

2. **Dashboard Layout**: All routes are nested under a single Dashboard component (`src/views/Dashboard.vue`) using Vue Router's nested routing

3. **API Service Layer**: `src/services/api.js` handles all AI service integrations with streaming support, token management, and error handling

4. **Local Data Persistence**: Uses localStorage for all user data (novels, prompts, settings, API configs)

### State Management Architecture

The app uses a single Pinia store (`novel.js`) that manages:
- **Novel Content**: Current novel, generated content, chapters
- **AI Features**: Outline generation, chapter content, AI chat
- **User Data**: Templates, characters, world settings, corpus
- **API Configuration**: AI service settings, model selection
- **Writing Tools**: Goals, statistics, analysis data
- **Token Billing**: Usage tracking and cost analysis

### API Integration Pattern

**Streaming Support**: All content generation uses streaming APIs (`generateTextStream`) for real-time output
**Error Handling**: Comprehensive error handling with user-friendly messages
**Token Management**: Automatic token estimation and usage tracking via `billingService`
**Multi-Provider Support**: Supports OpenAI, Anthropic, Google, DeepSeek, and other OpenAI-compatible APIs

## Key Features & Modules

### Core Writing Features
- **Novel Management**: Create, edit, and manage long-form novels with chapter system
- **Short Story Creation**: Rapid short story generation with templates
- **AI Writing Assistant**: Smart continuation, content polishing, character generation
- **Rich Text Editor**: WangEditor integration with AI assistant features

### AI Tools Library (10 specialized tools)
- Chapter outline generator, character creator, plot idea generator
- Title generator, theme generator, world-building tool
- Golden finger system generator, opening generator
- Synopsis generator, conflict generator

### Analysis & Management
- **Book Analysis**: Upload and analyze existing works (TXT/DOCX)
- **Writing Goals**: Daily/weekly/monthly targets with progress tracking
- **Token Billing**: Cost tracking across different AI services
- **Prompt Library**: Template management with variable substitution

### Data Management
- **Backup/Restore**: Full data export/import functionality
- **Local Storage**: No cloud dependencies, all data stored locally
- **Cross-Module Integration**: Data flows between novels, prompts, characters, and world settings

## Development Guidelines

### Component Patterns
- Use Composition API with `<script setup>` syntax
- Import Element Plus components via auto-import (configured in vite.config.js)
- Access store via `const novelStore = useNovelStore()`

### API Integration
- All AI calls should use `apiService.generateTextStream()` for streaming
- Handle errors gracefully with user notifications
- Record token usage via `billingService.recordAPICall()`

### Local Data Patterns
- Use localStorage for persistence with try/catch error handling  
- Implement data validation when loading from localStorage
- Provide fallback defaults for missing or corrupted data

### Styling Approach
- Element Plus provides base component styling
- Global styles in `src/style.css`
- Component-specific styles use scoped CSS

## Testing & Quality

### Code Quality
- ESLint with Vue and Prettier configurations
- Auto-import for Vue, Vue Router, and Pinia APIs
- TypeScript types generated automatically for auto-imports

### Development Workflow
1. Use `pnpm dev` for development with hot reload
2. Run `pnpm lint` before committing to fix code issues
3. Test in browser - no automated test suite currently configured
4. Build with `pnpm build` and test with `pnpm preview`

## Important Notes

### AI Service Configuration
- Supports multiple AI providers via OpenAI-compatible APIs
- API keys are stored locally and never transmitted to servers
- No built-in AI services - users must configure their own API access

### Data Privacy
- Pure frontend application - no backend server
- All user data stored in browser localStorage
- No cloud synchronization or data transmission

### Deployment
- Vite builds static files for deployment
- Uses hash routing (`createWebHashHistory`) for compatibility
- Base path configured as `'./'` for flexible deployment locations