// API诊断工具
// 帮助用户诊断和修复API连接问题

import { ElMessage, ElNotification } from 'element-plus'

class APIDiagnostic {
  constructor(apiService) {
    this.apiService = apiService
  }

  // 综合诊断API配置
  async runDiagnostic() {
    const results = {
      configCheck: false,
      networkCheck: false,
      authCheck: false,
      endpointCheck: false,
      suggestions: []
    }

    try {
      console.log('🔍 开始API诊断...')
      
      // 1. 检查基础配置
      results.configCheck = this.checkBasicConfig()
      if (!results.configCheck) {
        results.suggestions.push('请在设置中配置API密钥和服务地址')
        return results
      }

      // 2. 检查网络连接
      results.networkCheck = await this.checkNetworkConnection()
      if (!results.networkCheck) {
        results.suggestions.push('网络连接失败，请检查网络设置或防火墙配置')
      }

      // 3. 检查API密钥验证
      results.authCheck = await this.checkAPIAuthentication()
      if (!results.authCheck) {
        results.suggestions.push('API密钥验证失败，请检查密钥是否正确且有效')
      }

      // 4. 检查API端点
      results.endpointCheck = await this.checkAPIEndpoints()
      if (!results.endpointCheck) {
        results.suggestions.push('API端点配置可能有误，请检查服务地址配置')
      }

      // 生成修复建议
      this.generateSuggestions(results)
      
      return results
    } catch (error) {
      console.error('诊断过程中出现错误:', error)
      results.suggestions.push(`诊断失败: ${error.message}`)
      return results
    }
  }

  // 检查基础配置
  checkBasicConfig() {
    const config = this.apiService.getConfig()
    
    if (!config.apiKey || config.apiKey.trim() === '') {
      console.log('❌ API密钥未配置')
      return false
    }
    
    if (!config.baseURL || config.baseURL.trim() === '') {
      console.log('❌ API服务地址未配置')
      return false
    }
    
    console.log('✅ 基础配置检查通过')
    return true
  }

  // 检查网络连接
  async checkNetworkConnection() {
    try {
      const config = this.apiService.getConfig()
      const response = await fetch(config.baseURL, {
        method: 'GET',
        timeout: 10000
      })
      
      if (response.ok || response.status < 500) {
        console.log('✅ 网络连接正常')
        return true
      } else {
        console.log(`❌ 网络连接异常: ${response.status}`)
        return false
      }
    } catch (error) {
      console.log(`❌ 网络连接失败: ${error.message}`)
      return false
    }
  }

  // 检查API认证
  async checkAPIAuthentication() {
    try {
      const result = await this.apiService.validateAPIKey()
      if (result) {
        console.log('✅ API密钥验证成功')
        return true
      } else {
        console.log('❌ API密钥验证失败')
        return false
      }
    } catch (error) {
      console.log(`❌ API认证检查失败: ${error.message}`)
      return false
    }
  }

  // 检查API端点
  async checkAPIEndpoints() {
    const testEndpoints = [
      '/v1/chat/completions',
      '/chat/completions',
      '/api/v1/chat/completions',
      '/openai/v1/chat/completions'
    ]

    for (const endpoint of testEndpoints) {
      try {
        const url = this.apiService.buildURL(endpoint)
        const headers = this.apiService.buildHeaders()
        
        const response = await fetch(url, {
          method: 'POST',
          headers,
          body: JSON.stringify({
            model: 'gpt-3.5-turbo',
            messages: [{ role: 'user', content: 'test' }],
            max_tokens: 1
          }),
          timeout: 10000
        })

        if (response.ok) {
          console.log(`✅ 端点 ${endpoint} 可用`)
          return true
        } else if (response.status === 401) {
          console.log(`⚠️ 端点 ${endpoint} 认证失败，但端点存在`)
          // 端点存在但认证失败，说明端点配置正确
          return true
        } else {
          console.log(`❌ 端点 ${endpoint} 不可用: ${response.status}`)
        }
      } catch (error) {
        console.log(`❌ 端点 ${endpoint} 测试失败: ${error.message}`)
      }
    }

    return false
  }

  // 生成修复建议
  generateSuggestions(results) {
    if (!results.configCheck) {
      results.suggestions.push('配置API密钥和服务地址')
    }
    
    if (!results.networkCheck) {
      results.suggestions.push('检查网络连接和防火墙设置')
    }
    
    if (!results.authCheck) {
      results.suggestions.push('更新API密钥或检查账户状态')
    }
    
    if (!results.endpointCheck) {
      results.suggestions.push('联系API服务提供方确认正确的端点配置')
    }
    
    // 如果所有检查都通过
    if (results.configCheck && results.networkCheck && results.authCheck && results.endpointCheck) {
      results.suggestions.push('API配置正常，如果仍有问题请检查具体错误日志')
    }
  }

  // 显示诊断结果
  showDiagnosticResults(results) {
    const status = results.configCheck && results.networkCheck && results.authCheck && results.endpointCheck
    
    if (status) {
      ElNotification({
        title: '✅ API诊断通过',
        message: 'API配置正常，可以正常使用',
        type: 'success',
        duration: 3000
      })
    } else {
      const suggestionText = results.suggestions.join('\n• ')
      ElNotification({
        title: '❌ API诊断发现问题',
        message: `修复建议:\n• ${suggestionText}`,
        type: 'error',
        duration: 8000
      })
    }
    
    return status
  }

  // 快速修复常见问题
  async quickFix() {
    const config = this.apiService.getConfig()
    
    // 如果使用的是示例中的失效密钥，提示用户更新
    if (config.apiKey === 'sk-LdbDBBWQJq4BJP9h4Rgd4ZQa2nTMBQTtg4agp0hUO22DyCiz') {
      ElMessage.warning('检测到示例API密钥，请更新为您的有效密钥')
      return false
    }
    
    // 其他快速修复逻辑...
    return true
  }
}

export default APIDiagnostic